2025-08-06 17:51:36,621 INFO o.a.j.u.JMeterUtils: Setting Locale to en_EN
2025-08-06 17:51:36,629 INFO o.a.j.JMeter: Loading user properties from: user.properties
2025-08-06 17:51:36,630 INFO o.a.j.JMeter: Loading system properties from: system.properties
2025-08-06 17:51:36,634 INFO o.a.j.JMeter: Copyright (c) 1998-2024 The Apache Software Foundation
2025-08-06 17:51:36,634 INFO o.a.j.JMeter: Version 5.6.3
2025-08-06 17:51:36,634 INFO o.a.j.JMeter: java.version=1.8.0_441
2025-08-06 17:51:36,634 INFO o.a.j.JMeter: java.vm.name=Java HotSpot(TM) 64-Bit Server VM
2025-08-06 17:51:36,634 INFO o.a.j.JMeter: os.name=Windows 11
2025-08-06 17:51:36,634 INFO o.a.j.JMeter: os.arch=amd64
2025-08-06 17:51:36,634 INFO o.a.j.JMeter: os.version=10.0
2025-08-06 17:51:36,634 INFO o.a.j.JMeter: file.encoding=GBK
2025-08-06 17:51:36,634 INFO o.a.j.JMeter: java.awt.headless=null
2025-08-06 17:51:36,634 INFO o.a.j.JMeter: Max memory     =1073741824
2025-08-06 17:51:36,635 INFO o.a.j.JMeter: Available Processors =16
2025-08-06 17:51:36,637 INFO o.a.j.JMeter: Default Locale=English (EN)
2025-08-06 17:51:36,637 INFO o.a.j.JMeter: JMeter  Locale=English (EN)
2025-08-06 17:51:36,637 INFO o.a.j.JMeter: JMeterHome=D:\Soft\apache-jmeter-5.6.3
2025-08-06 17:51:36,637 INFO o.a.j.JMeter: user.dir  =D:\Soft\apache-jmeter-5.6.3\bin
2025-08-06 17:51:36,638 INFO o.a.j.JMeter: PWD       =D:\Soft\apache-jmeter-5.6.3\bin
2025-08-06 17:51:36,639 INFO o.a.j.JMeter: IP: ************* Name: WIN-3B63JOD1FRS FullName: host.docker.internal
2025-08-06 17:51:36,649 INFO o.a.j.JMeter: Loaded icon properties from org/apache/jmeter/images/icon.properties
2025-08-06 17:51:36,758 INFO o.a.j.JMeterGuiLauncher: Setting LAF to: com.github.weisj.darklaf.DarkLaf:com.github.weisj.darklaf.theme.DarculaTheme
2025-08-06 17:51:37,354 INFO o.a.j.r.ClassFinder: Will scan jar D:\Soft\apache-jmeter-5.6.3\lib\ext\mqtt-xmeter-2.0.2-jar-with-dependencies.jar with filter ExtendsClassFilter [parents=[interface org.apache.jmeter.gui.action.Command], inner=false, contains=null, notContains=null]. Consider exposing JMeter plugins via META-INF/services, and add JMeter-Skip-Class-Scanning=true manifest attribute so JMeter can skip classfile scanning
2025-08-06 17:51:37,883 INFO o.a.j.r.ClassFinder: Will scan jar D:\Soft\apache-jmeter-5.6.3\lib\ext\mqtt-xmeter-2.0.2-jar-with-dependencies.jar with filter ExtendsClassFilter [parents=[interface org.apache.jmeter.gui.plugin.MenuCreator], inner=false, contains=null, notContains=null]. Consider exposing JMeter plugins via META-INF/services, and add JMeter-Skip-Class-Scanning=true manifest attribute so JMeter can skip classfile scanning
2025-08-06 17:51:41,977 INFO o.a.j.s.FileServer: Default base='D:\Soft\apache-jmeter-5.6.3\bin'
2025-08-06 17:51:41,979 INFO o.a.j.g.a.Load: Loading file: E:\Mqtt-jemter\NewMQTTPlan.jmx
2025-08-06 17:51:41,979 INFO o.a.j.s.FileServer: Set new base='E:\Mqtt-jemter'
2025-08-06 17:51:42,051 INFO o.a.j.s.SaveService: Testplan (JMX) version: 2.2. Testlog (JTL) version: 2.2
2025-08-06 17:51:42,064 INFO o.a.j.s.SaveService: Using SaveService properties file encoding UTF-8
2025-08-06 17:51:42,064 INFO o.a.j.s.SaveService: Using SaveService properties version 5.0
2025-08-06 17:51:42,066 INFO o.a.j.s.SaveService: Loading file: E:\Mqtt-jemter\NewMQTTPlan.jmx
2025-08-06 17:51:42,162 INFO o.a.j.s.SampleResult: Note: Sample TimeStamps are START times
2025-08-06 17:51:42,162 INFO o.a.j.s.SampleResult: sampleresult.default.encoding is set to UTF-8
2025-08-06 17:51:42,162 INFO o.a.j.s.SampleResult: sampleresult.useNanoTime=true
2025-08-06 17:51:42,162 INFO o.a.j.s.SampleResult: sampleresult.nanoThreadSleep=5000
2025-08-06 17:51:42,259 INFO o.a.j.r.ClassFinder: Will scan jar D:\Soft\apache-jmeter-5.6.3\lib\ext\mqtt-xmeter-2.0.2-jar-with-dependencies.jar with filter ExtendsClassFilter [parents=[interface org.apache.jmeter.visualizers.ResultRenderer], inner=false, contains=null, notContains=null]. Consider exposing JMeter plugins via META-INF/services, and add JMeter-Skip-Class-Scanning=true manifest attribute so JMeter can skip classfile scanning
2025-08-06 17:51:42,293 INFO o.a.j.r.ClassFinder: Will scan jar D:\Soft\apache-jmeter-5.6.3\lib\ext\mqtt-xmeter-2.0.2-jar-with-dependencies.jar with filter ExtendsClassFilter [parents=[interface org.apache.jmeter.visualizers.RequestView], inner=false, contains=null, notContains=null]. Consider exposing JMeter plugins via META-INF/services, and add JMeter-Skip-Class-Scanning=true manifest attribute so JMeter can skip classfile scanning
2025-08-06 17:51:42,535 INFO o.a.j.r.ClassFinder: Will scan jar D:\Soft\apache-jmeter-5.6.3\lib\ext\mqtt-xmeter-2.0.2-jar-with-dependencies.jar with filter ExtendsClassFilter [parents=[interface org.apache.jmeter.visualizers.ResultRenderer], inner=false, contains=null, notContains=null]. Consider exposing JMeter plugins via META-INF/services, and add JMeter-Skip-Class-Scanning=true manifest attribute so JMeter can skip classfile scanning
2025-08-06 17:51:42,552 INFO o.a.j.r.ClassFinder: Will scan jar D:\Soft\apache-jmeter-5.6.3\lib\ext\mqtt-xmeter-2.0.2-jar-with-dependencies.jar with filter ExtendsClassFilter [parents=[interface org.apache.jmeter.visualizers.RequestView], inner=false, contains=null, notContains=null]. Consider exposing JMeter plugins via META-INF/services, and add JMeter-Skip-Class-Scanning=true manifest attribute so JMeter can skip classfile scanning
2025-08-06 17:51:42,616 INFO o.a.j.s.FileServer: Set new base='E:\Mqtt-jemter'
2025-08-06 17:53:44,012 INFO o.a.j.e.StandardJMeterEngine: Running the test!
2025-08-06 17:53:44,012 INFO o.a.j.s.SampleEvent: List of sample_variables: []
2025-08-06 17:53:44,012 INFO o.a.j.s.SampleEvent: List of sample_variables: []
2025-08-06 17:53:44,013 INFO o.a.j.e.u.CompoundVariable: Note: Function class names must contain the string: '.functions.'
2025-08-06 17:53:44,013 INFO o.a.j.e.u.CompoundVariable: Note: Function class names must not contain the string: '.gui.'
2025-08-06 17:53:44,172 INFO o.a.j.r.ClassFinder: Will scan jar D:\Soft\apache-jmeter-5.6.3\lib\ext\mqtt-xmeter-2.0.2-jar-with-dependencies.jar with filter ExtendsClassFilter [parents=[interface org.apache.jmeter.functions.Function], inner=false, contains=null, notContains=null]. Consider exposing JMeter plugins via META-INF/services, and add JMeter-Skip-Class-Scanning=true manifest attribute so JMeter can skip classfile scanning
2025-08-06 17:53:44,199 INFO o.a.j.g.u.JMeterMenuBar: setRunning(true, *local*)
2025-08-06 17:53:44,297 INFO o.a.j.e.StandardJMeterEngine: Starting ThreadGroup: 1 : Publish Group
2025-08-06 17:53:44,297 INFO o.a.j.e.StandardJMeterEngine: Starting 1 threads for group Publish Group.
2025-08-06 17:53:44,297 INFO o.a.j.e.StandardJMeterEngine: Thread will continue on error
2025-08-06 17:53:44,297 INFO o.a.j.t.ThreadGroup: Starting thread group... number=1 threads=1 ramp-up=1 delayedStart=false
2025-08-06 17:53:44,302 INFO o.a.j.t.ThreadGroup: Started thread group number 1
2025-08-06 17:53:44,302 INFO o.a.j.e.StandardJMeterEngine: All thread groups have been started
2025-08-06 17:53:44,303 INFO o.a.j.t.JMeterThread: Thread started: Publish Group 1-1
2025-08-06 17:53:44,794 INFO o.a.j.m.J.JSR223 PreProcessor: 最终拼接的 payloadHex 为：27081899A6DE01026940143E550142003FCA02008A2954040D1807121B5D133C00
2025-08-06 17:53:45,696 INFO o.a.j.m.J.JSR223 PreProcessor: 最终拼接的 payloadHex 为：27308F68A6DE01026940DC6555045D00133C2400314D360B0D1807121BC3026700
2025-08-06 17:53:45,823 INFO o.a.j.m.J.JSR223 PreProcessor: 最终拼接的 payloadHex 为：277E7EC4A6DE0102693D477A550374008EA3CE0098152D0A0D1807121BC7074A00
2025-08-06 17:53:45,958 INFO o.a.j.t.JMeterThread: Thread is done: Publish Group 1-1
2025-08-06 17:53:45,958 INFO o.a.j.t.JMeterThread: Thread finished: Publish Group 1-1
2025-08-06 17:53:45,959 INFO o.a.j.e.StandardJMeterEngine: Notifying test listeners of end of test
2025-08-06 17:53:45,969 INFO o.a.j.g.u.JMeterMenuBar: setRunning(false, *local*)