<?xml version="1.0" encoding="UTF-8"?>
<jmeterTestPlan version="1.2" properties="5.0" jmeter="5.6.3">
  <hashTree>
    <TestPlan guiclass="TestPlanGui" testclass="TestPlan" testname="MQTT性能测试计划">
      <elementProp name="TestPlan.user_defined_variables" elementType="Arguments" guiclass="ArgumentsPanel" testclass="Arguments" testname="用户定义的变量">
        <collectionProp name="Arguments.arguments"/>
      </elementProp>
      <boolProp name="TestPlan.functional_mode">false</boolProp>
      <boolProp name="TestPlan.serialize_threadgroups">false</boolProp>
    </TestPlan>
    <hashTree>
      <Arguments guiclass="ArgumentsPanel" testclass="Arguments" testname="xmeter_runtime_vars">
        <collectionProp name="Arguments.arguments">
          <elementProp name="server" elementType="Argument">
            <stringProp name="Argument.name">server</stringProp>
            <stringProp name="Argument.value">121.41.21.185</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">MQTT Broker地址</stringProp>
          </elementProp>
          <elementProp name="port" elementType="Argument">
            <stringProp name="Argument.name">port</stringProp>
            <stringProp name="Argument.value">1884</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">MQTT端口</stringProp>
          </elementProp>
          <elementProp name="clientIdPrefix" elementType="Argument">
            <stringProp name="Argument.name">clientIdPrefix</stringProp>
            <stringProp name="Argument.value">mqtt_test</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">客户端ID前缀</stringProp>
          </elementProp>
          <elementProp name="connKeepAlive" elementType="Argument">
            <stringProp name="Argument.name">connKeepAlive</stringProp>
            <stringProp name="Argument.value">60</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">连接保活时间(秒)</stringProp>
          </elementProp>
          <elementProp name="threadCount" elementType="Argument">
            <stringProp name="Argument.name">threadCount</stringProp>
            <stringProp name="Argument.value">1</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">并发客户端数量</stringProp>
          </elementProp>
          <elementProp name="rampUp" elementType="Argument">
            <stringProp name="Argument.name">rampUp</stringProp>
            <stringProp name="Argument.value">1</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">并发启动时长(秒)</stringProp>
          </elementProp>
          <elementProp name="loopCount" elementType="Argument">
            <stringProp name="Argument.name">loopCount</stringProp>
            <stringProp name="Argument.value">1</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">每个线程循环发布消息次数</stringProp>
          </elementProp>
          <elementProp name="publishInterval" elementType="Argument">
            <stringProp name="Argument.name">publishInterval</stringProp>
            <stringProp name="Argument.value">100</stringProp>
            <stringProp name="Argument.metadata">=</stringProp>
            <stringProp name="Argument.desc">发布消息间隔(毫秒)</stringProp>
          </elementProp>
        </collectionProp>
      </Arguments>
      <hashTree/>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Connection Group" enabled="false">
        <stringProp name="ThreadGroup.num_threads">${threadCount}</stringProp>
        <stringProp name="ThreadGroup.ramp_time">${rampUp}</stringProp>
        <boolProp name="ThreadGroup.same_user_on_next_iteration">true</boolProp>
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="循环控制器">
          <stringProp name="LoopController.loops">1</stringProp>
          <boolProp name="LoopController.continue_forever">false</boolProp>
        </elementProp>
      </ThreadGroup>
      <hashTree>
        <net.xmeter.samplers.ConnectSampler guiclass="net.xmeter.gui.ConnectSamplerUI" testclass="net.xmeter.samplers.ConnectSampler" testname="MQTT Connect" enabled="true">
          <stringProp name="mqtt.server">${server}</stringProp>
          <stringProp name="mqtt.port">${port}</stringProp>
          <stringProp name="mqtt.version">3.1.1</stringProp>
          <stringProp name="mqtt.conn_timeout">10</stringProp>
          <stringProp name="mqtt.protocol">TCP</stringProp>
          <stringProp name="mqtt.client_id_prefix">${clientIdPrefix}_conn</stringProp>
          <boolProp name="mqtt.client_id_suffix">true</boolProp>
          <stringProp name="mqtt.conn_keep_alive">${connKeepAlive}</stringProp>
          <stringProp name="mqtt.ws_path"></stringProp>
          <boolProp name="mqtt.dual_ssl_authentication">false</boolProp>
          <stringProp name="mqtt.clientcert_file_path"></stringProp>
          <stringProp name="mqtt.clientcert_password"></stringProp>
          <stringProp name="mqtt.user_name"></stringProp>
          <stringProp name="mqtt.password"></stringProp>
          <stringProp name="mqtt.conn_attampt_max">0</stringProp>
          <stringProp name="mqtt.reconn_attampt_max">0</stringProp>
          <stringProp name="mqtt.conn_clean_session">true</stringProp>
        </net.xmeter.samplers.ConnectSampler>
        <hashTree/>
        <net.xmeter.samplers.DisConnectSampler guiclass="net.xmeter.gui.DisConnectSamplerUI" testclass="net.xmeter.samplers.DisConnectSampler" testname="MQTT Disconnect" enabled="true"/>
        <hashTree/>
        <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\Mqtt-jemter\Logs\ConnectPubTest10000Loop3\results_tree.jtl</stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="SummaryReport" testclass="ResultCollector" testname="Connection Summary Report" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\Mqtt-jemter\Logs\ConnectPubTest10000Loop3\connection_summary.jtl</stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="StatVisualizer" testclass="ResultCollector" testname="Connection Aggregate Report" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\Mqtt-jemter\Logs\ConnectPubTest10000Loop3\connection_aggregate.jtl</stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="GraphVisualizer" testclass="ResultCollector" testname="Connection Response Time Graph" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\Mqtt-jemter\Logs\ConnectPubTest10000Loop3\connection_response_time.jtl</stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="RespTimeGraphVisualizer" testclass="ResultCollector" testname="Connection Aggregate Graph" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\Mqtt-jemter\Logs\ConnectPubTest10000Loop3\connection_aggregate_graph.jtl</stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="SimpleDataWriter" testclass="ResultCollector" testname="Connection Responses File" enabled="true">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>true</responseData>
              <samplerData>true</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>true</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\Mqtt-jemter\Logs\ConnectPubTest10000Loop3\connection_detailed.jtl</stringProp>
        </ResultCollector>
        <hashTree/>
      </hashTree>
      <ThreadGroup guiclass="ThreadGroupGui" testclass="ThreadGroup" testname="Publish Group">
        <stringProp name="ThreadGroup.num_threads">${threadCount}</stringProp>
        <stringProp name="ThreadGroup.ramp_time">${rampUp}</stringProp>
        <boolProp name="ThreadGroup.same_user_on_next_iteration">true</boolProp>
        <stringProp name="ThreadGroup.on_sample_error">continue</stringProp>
        <elementProp name="ThreadGroup.main_controller" elementType="LoopController" guiclass="LoopControlPanel" testclass="LoopController" testname="循环控制器">
          <stringProp name="LoopController.loops">${loopCount}</stringProp>
          <boolProp name="LoopController.continue_forever">false</boolProp>
        </elementProp>
      </ThreadGroup>
      <hashTree>
        <JSR223PreProcessor guiclass="TestBeanGUI" testclass="JSR223PreProcessor" testname="JSR223 PreProcessor">
          <stringProp name="cacheKey">true</stringProp>
          <stringProp name="filename"></stringProp>
          <stringProp name="parameters"></stringProp>
          <stringProp name="script">import java.time.*

// 定义东八区时间范围：2025-12-01 ~ 2026-01-01
ZoneId zone = ZoneId.of(&apos;Asia/Shanghai&apos;)
ZonedDateTime start = LocalDateTime.of(2025, 12, 1, 0, 0).atZone(zone)
ZonedDateTime end   = LocalDateTime.of(2026,  1, 1, 0, 0).atZone(zone)

// 随机 UTC 时间戳（单位：秒）
long randomSec = start.toEpochSecond() + (long)(Math.random() * (end.toEpochSecond() - start.toEpochSecond()))
String randomUtc = Long.toHexString(randomSec).toUpperCase().padLeft(8, &apos;0&apos;)

// 随机数生成辅助方法（固定长度 Hex）
def hex(num, digits) { Integer.toHexString(num).toUpperCase().padLeft(digits, &apos;0&apos;) }

def hexLittleEndian(num, digits) {
        if (digits != 4) {
            throw new IllegalArgumentException(&quot;Little-endian conversion only supports 4 digits (2 bytes)&quot;);
        }
        String hexStr = String.format(&quot;%04X&quot;, num);
        // 交换字节顺序：AABB -&gt; BBAA
        return hexStr.substring(2) + hexStr.substring(0, 2);
    }

// 随机值定义（你可根据需要调整范围）
int randomImei      = (int)(Math.random() * 10000000)
int randomIndex     = (int)(Math.random() * 2882)
int randomLongitude = (int)(Math.random() * 9000000) + 1000000         // 经度整数编码
int randomLatitude  = (int)(Math.random() * 9000000) + 1000000         // 纬度整数编码
int randomTtff      = (int)(Math.random() * 15) + 1                    // 定位时间 1–15 秒
int randomAltitude  = (int)(Math.random() * 5001)                      // 海拔 ≤ 5000 米
int randomSpeed     = (int)(Math.random() * 201)                       // 速度 ≤ 200 km/h


vars.put(&quot;randomImei&quot;, String.valueOf(randomImei))
// 存入 JMeter 变量
vars.put(&apos;imeiHex&apos;,       hex(randomImei, 6))
vars.put(&apos;randomUtc&apos;,     randomUtc)
vars.put(&apos;indexHex&apos;,      hex(randomIndex, 4))
vars.put(&apos;longitudeHex&apos;,  hex(randomLongitude, 8))
vars.put(&apos;latitudeHex&apos;,   hex(randomLatitude, 8))
vars.put(&apos;ttffHex&apos;,       hex(randomTtff, 2))
vars.put(&apos;altitudeHex&apos;,   hexLittleEndian(randomAltitude, 4))
vars.put(&apos;speedHex&apos;,      hexLittleEndian(randomSpeed, 4))

String payloadHex = &quot;27&quot; + vars.get(&quot;imeiHex&quot;) + &quot;A6DE0102&quot; + vars.get(&quot;randomUtc&quot;) + &quot;55&quot;+vars.get(&quot;indexHex&quot;)+vars.get(&quot;longitudeHex&quot;)+vars.get(&quot;latitudeHex&quot;)+vars.get(&quot;ttffHex&quot;)+&quot;0D1807121B&quot;+vars.get(&quot;altitudeHex&quot;)+vars.get(&quot;speedHex&quot;)
vars.put(&quot;finalHexPayload&quot;, payloadHex) // 可用于调试
log.info(&quot;最终拼接的 payloadHex 为：&quot; + payloadHex)</stringProp>
          <stringProp name="scriptLanguage">groovy</stringProp>
        </JSR223PreProcessor>
        <hashTree/>
        <net.xmeter.samplers.ConnectSampler guiclass="net.xmeter.gui.ConnectSamplerUI" testclass="net.xmeter.samplers.ConnectSampler" testname="MQTT Connect">
          <stringProp name="mqtt.server">${server}</stringProp>
          <stringProp name="mqtt.port">${port}</stringProp>
          <stringProp name="mqtt.version">3.1.1</stringProp>
          <stringProp name="mqtt.conn_timeout">10</stringProp>
          <stringProp name="mqtt.protocol"></stringProp>
          <stringProp name="mqtt.client_id_prefix">${clientIdPrefix}</stringProp>
          <boolProp name="mqtt.client_id_suffix">true</boolProp>
          <stringProp name="mqtt.conn_keep_alive">${connKeepAlive}</stringProp>
          <stringProp name="mqtt.ws_path"></stringProp>
          <boolProp name="mqtt.dual_ssl_authentication">false</boolProp>
          <stringProp name="mqtt.clientcert_file_path"></stringProp>
          <stringProp name="mqtt.clientcert_password"></stringProp>
          <stringProp name="mqtt.user_name"></stringProp>
          <stringProp name="mqtt.password"></stringProp>
          <stringProp name="mqtt.conn_attampt_max">0</stringProp>
          <stringProp name="mqtt.reconn_attampt_max">0</stringProp>
          <stringProp name="mqtt.conn_clean_session">true</stringProp>
        </net.xmeter.samplers.ConnectSampler>
        <hashTree/>
        <net.xmeter.samplers.PubSampler guiclass="net.xmeter.gui.PubSamplerUI" testclass="net.xmeter.samplers.PubSampler" testname="MQTT Publish">
          <stringProp name="mqtt.topic_name">/data-instance</stringProp>
          <stringProp name="mqtt.qos_level">0</stringProp>
          <boolProp name="mqtt.add_timestamp">false</boolProp>
          <stringProp name="mqtt.message_type">String</stringProp>
          <stringProp name="mqtt.message_to_sent">${finalHexPayload}</stringProp>
          <stringProp name="mqtt.message_type_fixed_length">1024</stringProp>
          <boolProp name="mqtt.retained_message">true</boolProp>
        </net.xmeter.samplers.PubSampler>
        <hashTree/>
        <ConstantTimer guiclass="ConstantTimerGui" testclass="ConstantTimer" testname="消息发布间隔">
          <stringProp name="ConstantTimer.delay">${publishInterval}</stringProp>
        </ConstantTimer>
        <hashTree/>
        <net.xmeter.samplers.DisConnectSampler guiclass="net.xmeter.gui.DisConnectSamplerUI" testclass="net.xmeter.samplers.DisConnectSampler" testname="MQTT Disconnect"/>
        <hashTree/>
        <ResultCollector guiclass="ViewResultsFullVisualizer" testclass="ResultCollector" testname="View Results Tree">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\Mqtt-jemter\Logs\ConnectPubTest10\results_tree.jtl</stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="SummaryReport" testclass="ResultCollector" testname="Publish Summary Report">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\Mqtt-jemter\Logs\ConnectPubTest10\publish_summary.jtl</stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="StatVisualizer" testclass="ResultCollector" testname="Publish Aggregate Report">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\Mqtt-jemter\Logs\ConnectPubTest10\publish_aggregate.jtl</stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="GraphVisualizer" testclass="ResultCollector" testname="Publish Response Time Graph">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\Mqtt-jemter\Logs\ConnectPubTest10\publish_response_time.jtl</stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="RespTimeGraphVisualizer" testclass="ResultCollector" testname="Publish Aggregate Graph">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>false</responseData>
              <samplerData>false</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>false</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\Mqtt-jemter\Logs\ConnectPubTest10\publish_aggregate_graph.jtl</stringProp>
          <stringProp name="RespTimeGraph.graphtitle">Publish Aggregate Graph</stringProp>
        </ResultCollector>
        <hashTree/>
        <ResultCollector guiclass="SimpleDataWriter" testclass="ResultCollector" testname="Publish Responses File">
          <boolProp name="ResultCollector.error_logging">false</boolProp>
          <objProp>
            <name>saveConfig</name>
            <value class="SampleSaveConfiguration">
              <time>true</time>
              <latency>true</latency>
              <timestamp>true</timestamp>
              <success>true</success>
              <label>true</label>
              <code>true</code>
              <message>true</message>
              <threadName>true</threadName>
              <dataType>true</dataType>
              <encoding>false</encoding>
              <assertions>true</assertions>
              <subresults>true</subresults>
              <responseData>true</responseData>
              <samplerData>true</samplerData>
              <xml>false</xml>
              <fieldNames>true</fieldNames>
              <responseHeaders>false</responseHeaders>
              <requestHeaders>false</requestHeaders>
              <responseDataOnError>true</responseDataOnError>
              <saveAssertionResultsFailureMessage>true</saveAssertionResultsFailureMessage>
              <assertionsResultsToSave>0</assertionsResultsToSave>
              <bytes>true</bytes>
              <sentBytes>true</sentBytes>
              <url>true</url>
              <threadCounts>true</threadCounts>
              <idleTime>true</idleTime>
              <connectTime>true</connectTime>
            </value>
          </objProp>
          <stringProp name="filename">E:\Mqtt-jemter\Logs\ConnectPubTest10\publish_detailed.jtl</stringProp>
        </ResultCollector>
        <hashTree/>
      </hashTree>
    </hashTree>
  </hashTree>
</jmeterTestPlan>
